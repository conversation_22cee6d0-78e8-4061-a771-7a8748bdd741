/**
 * i18n Ally 测试组件
 * 
 * 用于测试各种命名空间的翻译功能
 * 在 VSCode 中打开此文件，测试 i18n Ally 的功能：
 * 1. 自动完成
 * 2. 内联翻译显示
 * 3. 翻译键提示
 */

import { useTranslations } from 'next-intl';

export function I18nTestComponent() {
  // 测试各种命名空间
  const tCommon = useTranslations('common');
  const tHomepage = useTranslations('homepage');
  const tAuth = useTranslations('auth');
  const tPublish = useTranslations('publish');
  const tMyTasks = useTranslations('my-published-tasks');
  const tLegalTerms = useTranslations('legal.terms');
  const tLegalPrivacy = useTranslations('legal.privacy');
  const tTasksConfirm = useTranslations('tasks.confirmDialog');
  const tTasksDetail = useTranslations('tasks.taskDetail');
  const tAcceptedTasks = useTranslations('my-accepted-tasks');
  
  return (
    <div className="p-6 space-y-4">
      <h1 className="text-2xl font-bold">i18n Ally 测试页面</h1>
      
      {/* 测试 common 命名空间 */}
      <section>
        <h2 className="text-lg font-semibold">Common 命名空间测试</h2>
        <p>在下面输入 tCommon(' 测试自动完成：</p>
        <div className="bg-gray-100 p-2 rounded">
          {/* 在这里测试：tCommon('') */}
        </div>
      </section>
      
      {/* 测试 homepage 命名空间 */}
      <section>
        <h2 className="text-lg font-semibold">Homepage 命名空间测试</h2>
        <p>在下面输入 tHomepage(' 测试自动完成：</p>
        <div className="bg-gray-100 p-2 rounded">
          {/* 在这里测试：tHomepage('') */}
        </div>
      </section>
      
      {/* 测试 auth 命名空间 */}
      <section>
        <h2 className="text-lg font-semibold">Auth 命名空间测试</h2>
        <p>在下面输入 tAuth(' 测试自动完成：</p>
        <div className="bg-gray-100 p-2 rounded">
          {/* 在这里测试：tAuth('') */}
        </div>
      </section>
      
      {/* 测试 publish 命名空间 */}
      <section>
        <h2 className="text-lg font-semibold">Publish 命名空间测试</h2>
        <p>在下面输入 tPublish(' 测试自动完成：</p>
        <div className="bg-gray-100 p-2 rounded">
          {/* 在这里测试：tPublish('') */}
        </div>
      </section>
      
      {/* 测试 my-published-tasks 命名空间 */}
      <section>
        <h2 className="text-lg font-semibold">My Published Tasks 命名空间测试</h2>
        <p>在下面输入 tMyTasks(' 测试自动完成：</p>
        <div className="bg-gray-100 p-2 rounded">
          {/* 在这里测试：tMyTasks('') */}
        </div>
      </section>
      
      {/* 测试 legal.terms 命名空间 */}
      <section>
        <h2 className="text-lg font-semibold">Legal Terms 命名空间测试</h2>
        <p>在下面输入 tLegalTerms(' 测试自动完成：</p>
        <div className="bg-gray-100 p-2 rounded">
          {/* 在这里测试：tLegalTerms('') */}
        </div>
      </section>
      
      {/* 测试 legal.privacy 命名空间 */}
      <section>
        <h2 className="text-lg font-semibold">Legal Privacy 命名空间测试</h2>
        <p>在下面输入 tLegalPrivacy(' 测试自动完成：</p>
        <div className="bg-gray-100 p-2 rounded">
          {/* 在这里测试：tLegalPrivacy('') */}
        </div>
      </section>

      {/* 测试 tasks.confirmDialog 命名空间 */}
      <section>
        <h2 className="text-lg font-semibold">Tasks Confirm Dialog 命名空间测试</h2>
        <p>在下面输入 tTasksConfirm(' 测试自动完成：</p>
        <div className="bg-gray-100 p-2 rounded">
          {/* 在这里测试：tTasksConfirm('') */}
        </div>
      </section>

      {/* 测试 tasks.taskDetail 命名空间 */}
      <section>
        <h2 className="text-lg font-semibold">Tasks Detail 命名空间测试</h2>
        <p>在下面输入 tTasksDetail(' 测试自动完成：</p>
        <div className="bg-gray-100 p-2 rounded">
          {/* 在这里测试：tTasksDetail('') */}
        </div>
      </section>

      {/* 测试 my-accepted-tasks 命名空间 */}
      <section>
        <h2 className="text-lg font-semibold">My Accepted Tasks 命名空间测试</h2>
        <p>在下面输入 tAcceptedTasks(' 测试自动完成：</p>
        <div className="bg-gray-100 p-2 rounded">
          {/* 在这里测试：tAcceptedTasks('dialog.acceptTask.') */}
        </div>
      </section>
      
      {/* 实际使用示例 */}
      <section>
        <h2 className="text-lg font-semibold">实际使用示例</h2>
        <div className="space-y-2">
          <p>Legal Terms Title: {tLegalTerms('title')}</p>
          <p>Legal Privacy Title: {tLegalPrivacy('title')}</p>
          <p>My Tasks Navigation: {tMyTasks('navigation.title')}</p>
          <p>Tasks Confirm Title: {tTasksConfirm('title')}</p>
          <p>Tasks Detail Title: {tTasksDetail('title')}</p>
          <p>Accept Task Dialog: {tAcceptedTasks('dialog.acceptTask.title')}</p>
        </div>
      </section>
    </div>
  );
}

/**
 * 使用说明：
 * 
 * 1. 在 VSCode 中打开此文件
 * 2. 确保 i18n Ally 扩展已安装并启用
 * 3. 在注释区域输入翻译调用，如：tCommon('
 * 4. 观察是否出现自动完成提示
 * 5. 检查是否显示内联翻译
 * 6. 查看侧边栏的 i18n Ally 面板
 * 
 * 如果功能正常，您应该看到：
 * - 输入 t(' 时出现翻译键提示
 * - 代码中显示翻译内容的内联提示
 * - 侧边栏显示所有可用的翻译键
 * - 缺失翻译会被高亮显示
 */
