#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const filePath = path.join(process.cwd(), 'src/components/accept-task-dialog.tsx');

console.log('🔧 快速修复 AcceptTaskDialog 翻译键...');

if (!fs.existsSync(filePath)) {
  console.error('❌ 文件不存在');
  process.exit(1);
}

let content = fs.readFileSync(filePath, 'utf8');

// 批量替换 acceptTask. 为 dialog.acceptTask.
content = content.replace(/t\('acceptTask\./g, "t('dialog.acceptTask.");

fs.writeFileSync(filePath, content, 'utf8');

console.log('✅ 修复完成！');
