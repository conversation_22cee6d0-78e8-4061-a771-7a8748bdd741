-- 添加业务数据：平台、拒付类型、支付方式、商品分类

-- 添加平台数据
INSERT INTO "Platform" (id, name, status, "createdAt", "updatedAt")
VALUES 
  (substring(gen_random_uuid()::text, 1, 25), 'DHgate', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '独立站', 'ACTIVE', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- 添加拒付类型数据（包含费率）
INSERT INTO "ChargebackType" (id, name, rate, status, "createdAt", "updatedAt")
VALUES 
  (substring(gen_random_uuid()::text, 1, 25), '违反信用卡支付政策', 0.08, 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '违反PayPal支付政策', 0.09, 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '违反运输守则', 0.06, 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '违反健康守则', 0.10, 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '仿牌产品', 0.12, 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '禁售产品', 0.15, 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '虚假宣传', 0.07, 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '侵权产品', 0.12, 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '未授权产品', 0.10, 'ACTIVE', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- 添加支付方式数据（包含费率）
INSERT INTO "PaymentMethod" (id, name, rate, status, "createdAt", "updatedAt")
VALUES 
  (substring(gen_random_uuid()::text, 1, 25), '信用卡', 0.035, 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), 'PayPal', 0.045, 'ACTIVE', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- 添加商品分类数据
INSERT INTO "Category" (id, name, status, "createdAt", "updatedAt")
VALUES 
  (substring(gen_random_uuid()::text, 1, 25), '女装', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '男装', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '鞋类', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '包袋箱包', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '饰品与配饰', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '美容与个人护理', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '手机与配件', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '消费电子', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '电脑与办公', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '家用电器', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '家居与园艺', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '厨房与餐饮用品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '家纺', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '灯具与照明', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '婚庆与派对用品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '玩具与礼品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '母婴与儿童用品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '宠物用品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '运动与户外用品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '汽车与摩托车配件', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '手表与眼镜', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '办公与文具用品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '工具与工业设备', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '安防与监控', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '健康与保健', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '节日装饰与定制产品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '宗教与灵修用品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '艺术与手工艺', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '乐器与音响设备', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '图书与教育产品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '虚拟商品与订阅服务', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '二手与再制造商品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '电子烟与配件', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '成人用品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '医疗器械与检测设备', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '液体与化学品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '粉末类与易制品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '危险与仿真类物品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '敏感设计商品', 'ACTIVE', NOW(), NOW()),
  (substring(gen_random_uuid()::text, 1, 25), '特殊认证与许可产品', 'ACTIVE', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;
