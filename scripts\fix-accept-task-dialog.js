#!/usr/bin/env node

/**
 * 修复 AcceptTaskDialog 组件的翻译键路径
 * 
 * 将 acceptTask.* 转换为 dialog.acceptTask.*
 */

const fs = require('fs');
const path = require('path');

/**
 * 修复文件中的翻译键
 */
function fixAcceptTaskDialog() {
  const filePath = path.join(process.cwd(), 'src/components/accept-task-dialog.tsx');
  
  console.log('🔧 修复 AcceptTaskDialog 翻译键...\n');
  
  if (!fs.existsSync(filePath)) {
    console.error('❌ 文件不存在:', filePath);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 需要修复的翻译键映射
  const keyMappings = [
    // 基本键
    { old: "t('acceptTask.", new: "t('dialog.acceptTask." },
    
    // 特殊情况 - 错误处理
    { old: "t('error.accept')", new: "t('hooks.acceptTask.errors.networkError')" },
    
    // messages 命名空间的键保持不变，但需要确保正确
    // tMessages 调用保持不变
  ];
  
  keyMappings.forEach(mapping => {
    const oldPattern = mapping.old;
    const newPattern = mapping.new;
    
    if (content.includes(oldPattern)) {
      // 使用全局替换
      const regex = new RegExp(oldPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      content = content.replace(regex, newPattern);
      modified = true;
      console.log(`✅ 替换: ${oldPattern} -> ${newPattern}`);
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('\n💾 文件已保存');
    return true;
  } else {
    console.log('\n⏭️  无需修改');
    return false;
  }
}

/**
 * 验证翻译键是否存在
 */
function validateTranslationKeys() {
  console.log('\n🔍 验证翻译键...\n');
  
  const locales = ['zh', 'en'];
  const requiredKeys = [
    'dialog.acceptTask.title',
    'dialog.acceptTask.description',
    'dialog.acceptTask.successDescription',
    'dialog.acceptTask.notSet',
    'dialog.acceptTask.taskInfo',
    'dialog.acceptTask.taskTitle',
    'dialog.acceptTask.productDescription',
    'dialog.acceptTask.platform',
    'dialog.acceptTask.category',
    'dialog.acceptTask.purchaseRequirements',
    'dialog.acceptTask.quantity',
    'dialog.acceptTask.pieces',
    'dialog.acceptTask.unitPrice',
    'dialog.acceptTask.totalPrice',
    'dialog.acceptTask.listingDuration',
    'dialog.acceptTask.hours',
    'dialog.acceptTask.shippingInfo',
    'dialog.acceptTask.recipient',
    'dialog.acceptTask.phone',
    'dialog.acceptTask.address',
    'dialog.acceptTask.timeRequirements',
    'dialog.acceptTask.deadline',
    'dialog.acceptTask.deadlineNote',
    'dialog.acceptTask.costBreakdown',
    'dialog.acceptTask.commission',
    'dialog.acceptTask.deposit',
    'dialog.acceptTask.netCommission',
    'dialog.acceptTask.depositNote',
    'dialog.acceptTask.importantReminder',
    'dialog.acceptTask.reminder1',
    'dialog.acceptTask.reminder2',
    'dialog.acceptTask.reminder3',
    'dialog.acceptTask.reminder4',
    'dialog.acceptTask.reminder5',
    'dialog.acceptTask.cancel',
    'dialog.acceptTask.accepting',
    'dialog.acceptTask.confirmAccept',
    'dialog.acceptTask.agreementText',
    'dialog.acceptTask.serviceAgreement',
    'dialog.acceptTask.and',
    'dialog.acceptTask.userGuidelines',
    'hooks.acceptTask.errors.networkError'
  ];
  
  locales.forEach(locale => {
    const filePath = path.join(process.cwd(), 'messages', locale, 'my-accepted-tasks.json');
    
    if (fs.existsSync(filePath)) {
      console.log(`📄 检查 ${locale}/my-accepted-tasks.json`);
      
      try {
        const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        requiredKeys.forEach(key => {
          const keyParts = key.split('.');
          let current = content;
          let exists = true;
          
          for (const part of keyParts) {
            if (current && typeof current === 'object' && part in current) {
              current = current[part];
            } else {
              exists = false;
              break;
            }
          }
          
          if (exists) {
            console.log(`  ✅ ${key}`);
          } else {
            console.log(`  ❌ ${key} - 缺失`);
          }
        });
        
      } catch (error) {
        console.log(`  ❌ JSON 格式错误: ${error.message}`);
      }
    } else {
      console.log(`❌ ${locale}/my-accepted-tasks.json 不存在`);
    }
    
    console.log(''); // 空行分隔
  });
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 修复 AcceptTaskDialog 组件翻译\n');
  
  // 1. 修复翻译键
  const fixed = fixAcceptTaskDialog();
  
  // 2. 验证翻译键
  validateTranslationKeys();
  
  console.log('📋 修复结果:');
  if (fixed) {
    console.log('✅ 翻译键已修复');
  } else {
    console.log('ℹ️  无需修复');
  }
  
  console.log('\n📝 下一步：');
  console.log('1. 重启 VSCode');
  console.log('2. 打开 accept-task-dialog.tsx');
  console.log('3. 检查 i18n Ally 是否正确识别翻译');
  console.log('4. 测试对话框功能');
}

main();
