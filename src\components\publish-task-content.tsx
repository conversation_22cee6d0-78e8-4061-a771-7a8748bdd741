'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Circle, AlertTriangle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { useState } from 'react';
import { toast } from 'sonner';

import { BasicInfoStep } from '@/components/publish-steps/basic-info';
import { EvidenceUploadStep } from '@/components/publish-steps/evidence-upload';
import { PlatformSelectionStep } from '@/components/publish-steps/platform-selection';
import { SummaryStep } from '@/components/publish-steps/summary';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { usePublishConfig } from '@/hooks/use-publish-config';
import { usePublishTask, preparePublishData } from '@/hooks/use-publish-task';
import { useUserSystemRate } from '@/hooks/use-rates-api';
import { useUserMembership } from '@/hooks/use-user-membership';
import { useUserWallet } from '@/hooks/use-wallet';
import { WhitelistCheckResult } from '@/hooks/use-whitelist-check';
import {
  PublishTaskForm,
  PublishStep,
  PUBLISH_STEP_LABELS,
  FeeCalculation,
  EVIDENCE_UPLOAD_LABELS,
  EvidenceUploadType,
} from '@/lib/types/publish';

export function PublishTaskContent() {
  const t = useTranslations('publish');
  const tMessages = useTranslations('messages');

  const [currentStep, setCurrentStep] = useState<PublishStep>(
    PublishStep.PLATFORM_SELECTION,
  );
  const [formData, setFormData] = useState<PublishTaskForm>({
    chargebackTypes: [],
    paymentMethods: [],
    listingTime: '48', // 默认48小时上架时长
    evidenceUploadType: EvidenceUploadType.IMMEDIATE, // 默认选择立即上传
  });
  const [whitelistCheckResult, setWhitelistCheckResult] =
    useState<WhitelistCheckResult | null>(null);

  // 获取后台配置数据
  const { chargebackTypes, paymentMethods } = usePublishConfig();
  const { data: systemRate } = useUserSystemRate();
  const { data: userMembership } = useUserMembership();
  const { data: userWallet } = useUserWallet();

  // 发布委托hook
  const publishTaskMutation = usePublishTask();

  // 计算费用
  const calculateFees = (): FeeCalculation => {
    // 计算商品总价：数量 × 单价
    const productPrice = (formData.unitPrice || 0) * (formData.quantity || 1);

    // 支付方式费率（取最低）- 归属接单者
    const selectedPaymentMethods = paymentMethods.filter(method =>
      formData.paymentMethods.includes(method.id),
    );
    const paymentRates = selectedPaymentMethods.map(method => method.rate);
    const paymentMethodRate =
      paymentRates.length > 0 ? Math.min(...paymentRates) : 0;
    const paymentMethodFee = (productPrice * paymentMethodRate) / 100;

    // 拒付类型费率（取最低）- 归属接单者
    const selectedChargebackTypes = chargebackTypes.filter(type =>
      formData.chargebackTypes.includes(type.id),
    );
    const chargebackRates = selectedChargebackTypes.map(type => type.rate);
    const chargebackTypeRate =
      chargebackRates.length > 0 ? Math.min(...chargebackRates) : 0;
    const chargebackTypeFee = (productPrice * chargebackTypeRate) / 100;

    // 证据费用（所有类型都先收取证据费用，审核通过后退还）- 归属接单者
    let evidenceRate = 0;
    let evidenceFee = 0;

    if (formData.evidenceUploadType && systemRate) {
      // 所有证据上传类型都收取证据费用
      evidenceRate = systemRate.noEvidenceExtraRate;
      evidenceFee = (productPrice * evidenceRate) / 100;
    }

    // 会员服务费（总价 × 会员套餐的费率）- 归属平台
    let memberServiceRate = 0;
    let memberServiceFee = 0;

    // 如果用户有会员套餐，使用套餐的platformRate字段作为会员服务费率
    if (userMembership?.membershipPlan?.platformRate) {
      memberServiceRate = userMembership.membershipPlan.platformRate;
      memberServiceFee = (productPrice * memberServiceRate) / 100;
    }

    // 会员折扣计算（移除原有的会员折扣逻辑，因为现在直接使用会员费率）
    const memberDiscountRate = 0;
    const memberDiscount = 0;

    // 原价总费用
    const originalTotal =
      paymentMethodFee + chargebackTypeFee + evidenceFee + memberServiceFee;

    // 最终费用（原价总费用 - 会员折扣）
    const finalTotal = originalTotal - memberDiscount;

    return {
      productPrice,
      paymentMethodFee,
      paymentMethodRate,
      chargebackTypeFee,
      chargebackTypeRate,
      evidenceFee,
      evidenceRate,
      memberServiceFee,
      memberServiceRate,
      originalTotal,
      memberDiscount,
      memberDiscountRate,
      finalTotal,
    };
  };

  const updateFormData = (updates: Partial<PublishTaskForm>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const nextStep = () => {
    if (currentStep < PublishStep.SUMMARY) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > PublishStep.PLATFORM_SELECTION) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepProgress = () => {
    return (
      ((currentStep - 1) / (Object.keys(PublishStep).length / 2 - 1)) * 100
    );
  };

  const isStepCompleted = (step: PublishStep) => {
    return currentStep > step;
  };

  const isStepCurrent = (step: PublishStep) => {
    return currentStep === step;
  };

  const getStepLabel = (step: PublishStep) => {
    switch (step) {
      case PublishStep.PLATFORM_SELECTION:
        return t('steps.platformSelection');
      case PublishStep.BASIC_INFO:
        return t('steps.basicInfo');
      case PublishStep.EVIDENCE_UPLOAD:
        return t('steps.evidenceUpload');
      case PublishStep.SUMMARY:
        return t('steps.summary');
      default:
        return '';
    }
  };

  const canProceedToNext = () => {
    switch (currentStep) {
      case PublishStep.PLATFORM_SELECTION:
        return (
          formData.platform &&
          formData.category &&
          formData.chargebackTypes.length > 0 &&
          formData.paymentMethods.length > 0
        );
      case PublishStep.BASIC_INFO: {
        // 基本信息验证
        const basicInfoValid =
          formData.productUrl &&
          formData.productDescription &&
          formData.quantity &&
          formData.unitPrice &&
          formData.listingTime &&
          formData.recipientName &&
          formData.recipientPhone &&
          formData.shippingAddress &&
          formData.cartScreenshot &&
          formData.cartScreenshot.length > 0; // 购物车截图必填

        // 白名单检测验证 - 如果检测到白名单，阻止继续
        if (whitelistCheckResult?.isWhitelisted) {
          return false;
        }

        return basicInfoValid;
      }
      case PublishStep.EVIDENCE_UPLOAD:
        if (formData.evidenceUploadType === undefined) return false;
        if (formData.evidenceUploadType === EvidenceUploadType.IMMEDIATE) {
          return formData.evidenceFiles && formData.evidenceFiles.length > 0;
        }
        return true;
      default:
        return true;
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case PublishStep.PLATFORM_SELECTION:
        return (
          <PlatformSelectionStep
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case PublishStep.BASIC_INFO:
        return (
          <BasicInfoStep
            formData={formData}
            updateFormData={updateFormData}
            onWhitelistResult={setWhitelistCheckResult}
          />
        );
      case PublishStep.EVIDENCE_UPLOAD:
        return (
          <EvidenceUploadStep
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case PublishStep.SUMMARY:
        return (
          <SummaryStep formData={formData} feeCalculation={calculateFees()} />
        );
      default:
        return null;
    }
  };

  return (
    <>
      {/* 页面标题 */}
      <div>
        <h1 className='text-2xl font-bold tracking-tight'>
          {t('navigation.title')}
        </h1>
        <p className='text-muted-foreground'>{t('navigation.description')}</p>
      </div>

      <div className='w-full space-y-6'>
        {/* 步骤指示器 */}
        <Card>
          <CardContent className='py-6'>
            <div className='relative flex items-center justify-between'>
              {/* 背景连接线 */}
              <div className='absolute top-4 left-8 right-8 h-px bg-border' />

              {/* 进度连接线 */}
              {/* eslint-disable-next-line tailwindcss/no-inline-style */}
              <div
                className='absolute top-4 left-8 h-px bg-primary transition-all duration-500 ease-in-out'
                style={{
                  width: `calc(${((currentStep - 1) / 3) * 100}% - 32px)`,
                }}
              />

              {Object.values(PublishStep)
                .filter(step => typeof step === 'number')
                .map((step, index) => (
                  <div
                    key={step}
                    className='flex flex-col items-center relative'
                  >
                    {/* 步骤点 */}
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-200 border-2 ${
                        isStepCompleted(step as PublishStep)
                          ? 'bg-primary text-primary-foreground border-primary'
                          : isStepCurrent(step as PublishStep)
                            ? 'bg-primary text-primary-foreground border-primary'
                            : 'bg-background text-muted-foreground border-muted'
                      }`}
                    >
                      {isStepCompleted(step as PublishStep) ? (
                        <CheckCircle className='h-4 w-4' />
                      ) : (
                        step
                      )}
                    </div>

                    {/* 步骤标题 */}
                    <span
                      className={`mt-2 text-xs font-medium transition-colors ${
                        isStepCurrent(step as PublishStep)
                          ? 'text-foreground'
                          : isStepCompleted(step as PublishStep)
                            ? 'text-foreground'
                            : 'text-muted-foreground'
                      }`}
                    >
                      {getStepLabel(step as PublishStep)}
                    </span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        {/* 当前步骤内容 */}
        <div className='space-y-6'>{renderStep()}</div>

        {/* 导航按钮 */}
        <Card>
          <CardContent className='pt-6'>
            {/* 白名单检测失败提示 */}
            {currentStep === PublishStep.BASIC_INFO &&
              whitelistCheckResult?.isWhitelisted && (
                <div className='mb-4 p-4 bg-red-50 border border-red-200 rounded-lg'>
                  <div className='flex items-center gap-2 text-red-800'>
                    <AlertTriangle className='h-4 w-4' />
                    <span className='font-medium'>
                      {t('dialog.whitelistBlocked.title')}
                    </span>
                  </div>
                  <p className='mt-1 text-sm text-red-700'>
                    {tMessages(whitelistCheckResult.message)}
                    {t('dialog.whitelistBlocked.description')}
                  </p>
                </div>
              )}

            <div className='flex items-center justify-center gap-3'>
              <Button
                variant='outline'
                onClick={prevStep}
                disabled={currentStep === PublishStep.PLATFORM_SELECTION}
              >
                {t('actions.previous')}
              </Button>

              {currentStep === PublishStep.SUMMARY ? (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button className='bg-green-600 hover:bg-green-700'>
                      {t('actions.submit')}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>
                        {t('dialog.confirmSubmit.title')}
                      </AlertDialogTitle>
                      <AlertDialogDescription>
                        {t('dialog.confirmSubmit.description')}
                      </AlertDialogDescription>
                      <div className='space-y-2 mt-4'>
                        <div className='flex justify-between'>
                          <span>{t('dialog.confirmSubmit.taskFee')}</span>
                          <strong className='text-destructive'>
                            ${calculateFees().finalTotal.toFixed(2)}
                          </strong>
                        </div>
                      </div>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>
                        {t('actions.cancel')}
                      </AlertDialogCancel>
                      <AlertDialogAction
                        onClick={async () => {
                          try {
                            const feeCalculation = calculateFees();
                            const currentBalance = userWallet?.balance || 0;

                            // 检查余额是否足够
                            if (currentBalance < feeCalculation.finalTotal) {
                              toast.error('余额不足', {
                                description: `当前余额：$${currentBalance.toFixed(2)}，需要：$${feeCalculation.finalTotal.toFixed(2)}`,
                                duration: 8000,
                                action: {
                                  label: '去充值',
                                  onClick: () =>
                                    window.open('/wallet', '_blank'),
                                },
                              });
                              return;
                            }

                            const publishData = preparePublishData(
                              formData,
                              feeCalculation.finalTotal,
                            );

                            await publishTaskMutation.mutateAsync(publishData);

                            // 成功后重置表单
                            setCurrentStep(PublishStep.PLATFORM_SELECTION);
                            setFormData({
                              chargebackTypes: [],
                              paymentMethods: [],
                              listingTime: '48', // 默认48小时上架时长
                              evidenceUploadType: EvidenceUploadType.IMMEDIATE, // 默认选择立即上传
                            });
                          } catch (error) {
                            // 错误处理已在hook中完成
                          }
                        }}
                      >
                        {t('actions.confirm')}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              ) : (
                <Button onClick={nextStep} disabled={!canProceedToNext()}>
                  {t('actions.next')}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
