'use client';

import { useQuery } from '@tanstack/react-query';
import {
  Users,
  UserPlus,
  ClipboardList,
  DollarSign,
  TrendingUp,
  Package,
  Clock,
  Eye,
} from 'lucide-react';
import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface AdminDashboardData {
  stats: {
    totalUsers: number;
    newUsers: number;
    totalTasks: number;
    totalRevenue: number;
    userGrowth: string;
    taskGrowth: string;
    revenueGrowth: string;
  };
  platformStats: Array<{
    platform: string;
    count: number;
    percentage: number;
    color: string;
  }>;
  categoryStats: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  recentTasks: Array<{
    id: string;
    platform: string;
    amount: number;
    status: string;
  }>;
  recentUsers: Array<{
    id: string;
    name: string;
    email: string;
    memberType: string;
  }>;
}

export function AdminDashboardContent() {
  // 获取管理员仪表盘统计数据
  const {
    data: dashboardData,
    isLoading,
    error,
  } = useQuery<{
    success: boolean;
    data: AdminDashboardData;
  }>({
    queryKey: ['admin-dashboard-stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/dashboard/stats');
      if (!response.ok) {
        throw new Error('获取仪表盘数据失败');
      }
      return response.json();
    },
    refetchInterval: 5 * 60 * 1000, // 5分钟刷新一次
  });

  if (isLoading) {
    return (
      <div className='space-y-6'>
        {/* 加载状态 */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index}>
              <CardContent className='pt-6'>
                <div className='animate-pulse space-y-3'>
                  <div className='h-4 bg-gray-200 rounded w-3/4'></div>
                  <div className='h-8 bg-gray-200 rounded w-1/2'></div>
                  <div className='h-3 bg-gray-200 rounded w-2/3'></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !dashboardData?.success) {
    return (
      <div className='space-y-6'>
        <Card>
          <CardContent className='pt-6'>
            <div className='text-center text-red-500'>
              加载仪表盘数据失败，请刷新页面重试
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const data = dashboardData.data;

  // 处理平台分布数据 - 只显示前4个，其余归为"其他"
  const platformStats = (() => {
    if (data.platformStats.length <= 4) {
      return data.platformStats;
    }

    const top4 = data.platformStats.slice(0, 4);
    const others = data.platformStats.slice(4);
    const othersTotal = others.reduce(
      (sum, item) => ({
        count: sum.count + item.count,
        percentage: sum.percentage + item.percentage,
      }),
      { count: 0, percentage: 0 },
    );

    if (othersTotal.count > 0) {
      return [
        ...top4,
        {
          platform: '其他',
          count: othersTotal.count,
          percentage: othersTotal.percentage,
          color: 'bg-gray-500',
        },
      ];
    }
    return top4;
  })();

  // 处理分类分布数据 - 只显示前4个，其余归为"其他"
  const categoryStats = (() => {
    if (data.categoryStats.length <= 4) {
      return data.categoryStats;
    }

    const top4 = data.categoryStats.slice(0, 4);
    const others = data.categoryStats.slice(4);
    const othersTotal = others.reduce(
      (sum, item) => ({
        count: sum.count + item.count,
        percentage: sum.percentage + item.percentage,
      }),
      { count: 0, percentage: 0 },
    );

    if (othersTotal.count > 0) {
      return [
        ...top4,
        {
          category: '其他',
          count: othersTotal.count,
          percentage: othersTotal.percentage,
        },
      ];
    }
    return top4;
  })();

  return (
    <div className='space-y-6'>
      {/* 核心数据统计 */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>注册用户总数</CardTitle>
            <Users className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {data.stats.totalUsers.toLocaleString()}
            </div>
            <p className='text-xs text-muted-foreground'>
              <span className='text-green-600'>{data.stats.userGrowth}</span>{' '}
              相比上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>新增用户数</CardTitle>
            <UserPlus className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{data.stats.newUsers}</div>
            <p className='text-xs text-muted-foreground'>本月新增注册用户</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>平台委托数</CardTitle>
            <ClipboardList className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {data.stats.totalTasks.toLocaleString()}
            </div>
            <p className='text-xs text-muted-foreground'>
              <span className='text-green-600'>{data.stats.taskGrowth}</span>{' '}
              相比上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>平台总收入</CardTitle>
            <DollarSign className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              ${data.stats.totalRevenue.toLocaleString()}
            </div>
            <p className='text-xs text-muted-foreground'>
              <span className='text-green-600'>{data.stats.revenueGrowth}</span>{' '}
              相比上月
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 平台分布和商品类型分布 */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* 平台分布 */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <TrendingUp className='h-5 w-5' />
              平台分布
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            {platformStats.length === 0 ? (
              <div className='text-center text-muted-foreground py-4'>
                暂无平台数据
              </div>
            ) : (
              platformStats.map(item => (
                <div key={item.platform} className='space-y-2'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm font-medium'>{item.platform}</span>
                    <span className='text-sm text-muted-foreground'>
                      {item.count} ({item.percentage}%)
                    </span>
                  </div>
                  <div className='relative'>
                    <div className='h-3 w-full bg-secondary rounded-full overflow-hidden'>
                      {/* eslint-disable-next-line tailwindcss/no-inline-style */}
                      <div
                        className={`h-full rounded-full transition-all ${item.color}`}
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* 商品类型分布 */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Package className='h-5 w-5' />
              商品类型分布
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            {categoryStats.length === 0 ? (
              <div className='text-center text-muted-foreground py-4'>
                暂无分类数据
              </div>
            ) : (
              categoryStats.map((item, index) => (
                <div key={item.category} className='space-y-2'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm font-medium'>{item.category}</span>
                    <span className='text-sm text-muted-foreground'>
                      {item.count} ({item.percentage}%)
                    </span>
                  </div>
                  <div className='relative'>
                    <div className='h-3 w-full bg-secondary rounded-full overflow-hidden'>
                      {/* eslint-disable-next-line tailwindcss/no-inline-style */}
                      <div
                        className={`h-full rounded-full transition-all ${
                          index === 0
                            ? 'bg-blue-500'
                            : index === 1
                              ? 'bg-green-500'
                              : index === 2
                                ? 'bg-purple-500'
                                : index === 3
                                  ? 'bg-yellow-500'
                                  : 'bg-gray-500'
                        }`}
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>

      {/* 最近委托和最近用户 */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* 最近委托 */}
        <Card>
          <CardHeader className='flex flex-row items-center justify-between'>
            <CardTitle className='flex items-center gap-2'>
              <Clock className='h-5 w-5' />
              最近委托
            </CardTitle>
            <Button variant='outline' size='sm' asChild>
              <Link href='/admin/tasks'>
                <Eye className='mr-2 h-4 w-4' />
                查看全部
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            {/* 表格头部 */}
            <div className='grid grid-cols-4 gap-4 pb-3 border-b text-sm font-medium text-muted-foreground'>
              <div>委托ID</div>
              <div>平台</div>
              <div>金额</div>
              <div>状态</div>
            </div>

            <div className='space-y-2 mt-3'>
              {data.recentTasks.length === 0 ? (
                <div className='text-center text-muted-foreground py-4'>
                  暂无最近委托
                </div>
              ) : (
                data.recentTasks.map(task => (
                  <div
                    key={task.id}
                    className='grid grid-cols-4 gap-4 items-center p-3 border rounded-lg hover:bg-accent/50 transition-colors'
                  >
                    <div className='font-mono text-sm font-medium text-blue-600'>
                      {task.id}
                    </div>
                    <div>
                      <Badge variant='outline' className='text-xs'>
                        {task.platform}
                      </Badge>
                    </div>
                    <div className='font-medium text-sm text-green-600'>
                      ${(task.amount || 0).toFixed(2)}
                    </div>
                    <div>
                      <Badge
                        variant={
                          task.status === '已完成'
                            ? 'default'
                            : task.status === '进行中'
                              ? 'secondary'
                              : 'outline'
                        }
                        className={`text-xs ${
                          task.status === '已完成'
                            ? 'bg-green-100 text-green-800'
                            : task.status === '进行中'
                              ? 'bg-blue-100 text-blue-800'
                              : ''
                        }`}
                      >
                        {task.status}
                      </Badge>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* 最近用户 */}
        <Card>
          <CardHeader className='flex flex-row items-center justify-between'>
            <CardTitle className='flex items-center gap-2'>
              <Users className='h-5 w-5' />
              最近用户
            </CardTitle>
            <Button variant='outline' size='sm' asChild>
              <Link href='/admin/users'>
                <Eye className='mr-2 h-4 w-4' />
                查看全部
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {data.recentUsers.length === 0 ? (
                <div className='text-center text-muted-foreground py-4'>
                  暂无最近用户
                </div>
              ) : (
                data.recentUsers.map(user => (
                  <div
                    key={user.id}
                    className='flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors'
                  >
                    <div className='space-y-1 flex-1'>
                      <p className='font-medium text-sm'>{user.name}</p>
                      <p className='text-xs text-muted-foreground'>
                        {user.email}
                      </p>
                    </div>
                    <div className='text-right ml-4'>
                      <Badge
                        variant={
                          user.memberType === '商业版'
                            ? 'default'
                            : user.memberType === '专业版'
                              ? 'secondary'
                              : 'outline'
                        }
                        className={`text-xs ${
                          user.memberType === '商业版'
                            ? 'bg-purple-100 text-purple-800'
                            : user.memberType === '专业版'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {user.memberType}
                      </Badge>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
